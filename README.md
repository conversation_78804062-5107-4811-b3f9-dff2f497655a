# ManyCam Account Checker with Proxy Support

برنامج فحص حسابات ManyCam مع دعم البروكسي المتقدم

## الميزات الجديدة

### دعم البروكسي
- **أنواع البروكسي المدعومة**: HTTP, HTTPS, SOCKS4, SOCKS5
- **تحميل ملف البروكسي**: إمكانية تحميل قائمة بروكسي من ملف خارجي
- **تدوير البروكسي**: تغيير البروكسي تلقائياً لكل طلب
- **مصادقة البروكسي**: دعم البروكسي مع اسم المستخدم وكلمة المرور

### صيغ البروكسي المدعومة

```
# HTTP/HTTPS Proxy
host:port
host:port:username:password

# SOCKS Proxy (يتم اكتشافه تلقائياً)
socks.example.com:1080
socks.example.com:1080:username:password

# أمثلة من ملف البروكسي
prox-fi.pointtoserver.com:10799:purevpn0s9739689:5hvds3nd
ist.socks.privado.io:1080:en45750scjoe:nj3dm6mmck19
************:37160:mf0oldzi:er4A9eLbGsay
```

## كيفية الاستخدام

### 1. تحضير ملف البروكسي
- أنشئ ملف نصي (.txt) يحتوي على قائمة البروكسي
- ضع كل بروكسي في سطر منفصل
- استخدم الصيغة: `host:port:username:password`

### 2. تشغيل البرنامج
1. شغل البرنامج: `python "manycam checker.py"`
2. اختر ملف الكومبو (Combo File)
3. اختر ملف البروكسي (Proxy File)
4. اضغط "Load" لتحميل البروكسي
5. فعل "Use Proxy" لاستخدام البروكسي
6. فعل "Rotate Proxy" لتدوير البروكسي
7. اضغط "Start Checking"

### 3. الإعدادات
- **Use Proxy**: تفعيل/تعطيل استخدام البروكسي
- **Rotate Proxy**: تدوير البروكسي لكل طلب (موصى به)

## معلومات الحالة

البرنامج يعرض:
- عدد البروكسي المحملة
- البروكسي الحالي المستخدم
- إحصائيات الفحص (المجموع، المفحوص، النجح، مع منتج)

## معالجة الأخطاء

البرنامج يتعامل مع:
- أخطاء البروكسي
- انتهاء المهلة الزمنية
- أخطاء الاتصال
- أخطاء التحليل

## المتطلبات

```bash
pip install requests PySocks
```

## ملاحظات مهمة

1. **الأمان**: استخدم بروكسي موثوق فقط
2. **السرعة**: البروكسي قد يبطئ عملية الفحص
3. **الاستقرار**: بعض البروكسي قد تكون غير مستقرة
4. **القانونية**: تأكد من الامتثال للقوانين المحلية

## استكشاف الأخطاء

### البروكسي لا يعمل
- تحقق من صحة صيغة البروكسي
- تأكد من أن البروكسي يعمل
- جرب بروكسي مختلف

### بطء في الاستجابة
- قلل عدد البروكسي
- استخدم بروكسي أسرع
- زد المهلة الزمنية في الكود

### أخطاء الاتصال
- تحقق من اتصال الإنترنت
- تأكد من أن البروكسي متاح
- جرب بدون بروكسي أولاً
